---
import Layout from '../../layouts/Layout.astro';
import ImageGallery from '../../components/ImageGallery.astro';
import StructuredData from '../../components/StructuredData.astro';
import RelatedImages from '../../components/RelatedImages.astro';
import { createPolarClient, transformPolarProduct, formatPrice } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

// Get slug from URL params
const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/products');
}

// Fetch product at runtime
let product: LocalProduct | null = null;
let error: string | null = null;

try {
  // Get runtime environment from Cloudflare context
  const env = Astro.locals?.runtime?.env;
  const polar = createPolarClient(env);
  const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;

  if (!organizationId) {
    error = 'Organization ID not configured';
  } else {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Find product by slug
    product = products.find(p => p.slug === slug) || null;

    if (!product) {
      return Astro.redirect('/products');
    }
  }
} catch (err) {
  console.error('Error fetching product:', err);
  return Astro.redirect('/products');
}

// Check if product exists and has required properties
if (!product || !product.id || !product.name) {
  return Astro.redirect('/products');
}

// Generate checkout URL
let checkoutUrl = '';
try {
  // Get runtime environment from Cloudflare context
  const env = Astro.locals?.runtime?.env;
  const polar = createPolarClient(env);
  const checkoutLink = await polar.checkoutLinks.create({
    paymentProcessor: 'stripe',
    productId: product.id,
    allowDiscountCodes: true,
    requireBillingAddress: false,
    successUrl: `${import.meta.env.PUBLIC_SITE_URL}/success`
  });
  checkoutUrl = checkoutLink.url;
} catch (error) {
  console.error('Error creating checkout URL:', error);
}

// Prepare structured data
const productUrl = `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/${product.slug}`;
const productStructuredData = {
  name: product.name,
  description: product.description,
  images: product.images,
  price: product.price,
  currency: product.currency,
  isAvailable: product.isAvailable,
  id: product.id,
  url: productUrl
};

// Breadcrumb data
const breadcrumbData = {
  items: [
    { name: "Home", url: import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store' },
    { name: "Products", url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products` },
    { name: product.name, url: productUrl }
  ]
};
---

<Layout
  title={`${product.name} - InfPik`}
  description={product.description}
  image={product.images[0] || '/og-image.jpg'}
  canonical={productUrl}
  type="product"
>
  <!-- Structured Data -->
  <StructuredData type="Product" data={productStructuredData} />
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />
  <div class="container max-w-7xl">
    <div class="mt-8 mb-8">
      <nav class="flex items-center gap-2 text-sm text-primary-600">
        <a href="/" class="hover:text-accent-600 transition-colors">Home</a>
        <span>/</span>
        <a href="/products" class="hover:text-accent-600 transition-colors">Products</a>
        <span>/</span>
        <span class="text-primary-900 font-medium">{product.name}</span>
      </nav>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <div class="lg:sticky lg:top-8 lg:self-start">
        <ImageGallery images={product.images} productName={product.name} />
      </div>

      <div class="space-y-8">
        <div>
          <h1 class="text-3xl lg:text-4xl font-bold text-primary-900 mb-6">{product.name}</h1>
        </div>

        <div class="prose prose-gray max-w-none">
          <h3 class="text-lg font-semibold text-primary-900 mb-3">Description</h3>
          <p class="text-primary-700 leading-relaxed">{product.description}</p>
        </div>

        {product.tags && product.tags.length > 0 && (
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Similar</h3>
            <div class="flex flex-wrap gap-2">
              {product.tags.map(tag => (
                <a
                  href={`/products/tag/${tag}`}
                  class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 hover:text-gray-900 transition-colors duration-200 cursor-pointer"
                >
                  #{tag}
                </a>
              ))}
            </div>
          </div>
        )}

        <div class="pt-6 border-t border-primary-200">
          <div class="flex gap-3">
            {checkoutUrl ? (
              <a
                href={checkoutUrl}
                class="flex-1 inline-flex items-center justify-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path>
                </svg>
                <span>Buy Now - {formatPrice(product.price, product.currency)}</span>
              </a>
            ) : (
              <button
                class="flex-1 bg-primary-300 text-primary-600 px-6 py-3 rounded-full font-semibold text-base cursor-not-allowed"
                disabled
              >
                Checkout Unavailable
              </button>
            )}

            <button
              class="inline-flex items-center justify-center gap-2 bg-primary-50 border-2 border-primary-200 text-primary-700 px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-primary-100 hover:border-primary-300 hover:text-primary-900"
              onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
              </svg>
              <span>Share</span>
            </button>
          </div>
        </div>

        <div class="bg-primary-50 rounded-2xl p-6">
          <h3 class="text-lg font-semibold text-primary-900 mb-4">Product Details</h3>
          <ul class="space-y-3 text-primary-700">
            <li class="flex items-start gap-3">
              <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>Format:</strong> High-resolution digital image</span>
            </li>
            <li class="flex items-start gap-3">
              <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>License:</strong> Commercial use allowed</span>
            </li>
            <li class="flex items-start gap-3">
              <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>Delivery:</strong> Instant download after purchase</span>
            </li>
            <li class="flex items-start gap-3">
              <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>Support:</strong> Email support included</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Related Images Section -->
    <RelatedImages currentProduct={product} />

    <div class="mt-16 text-center">
      <h2 class="text-3xl font-bold text-primary-900 mb-4">You might also like</h2>
      <p class="text-primary-600 mb-8">Browse our full collection of digital images</p>
      <a
        href="/products"
        class="inline-flex items-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5"
      >
        View All Products
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
        </svg>
      </a>
    </div>
  </div>
</Layout>



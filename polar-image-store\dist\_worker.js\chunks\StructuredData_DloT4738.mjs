globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead,b as addAttribute,s as spreadAttributes,u as unescapeHTML}from"./astro/server_BdgiS2eL.mjs";import{$ as $$Image}from"./_astro_assets_BhKiY6XE.mjs";import{getResponsiveImageUrls,getOptimizedImageUrl,generateSizesAttribute}from"./imageOptimization_DKmSV198.mjs";const $$Astro$1=createAstro("https://infpik.store"),$$OptimizedImage=createComponent((async(e,t,r)=>{const a=e.createAstro($$Astro$1,t,r);a.self=$$OptimizedImage;const{src:i,alt:s,width:o=800,height:n=600,quality:m=85,format:p="auto",fit:c="scale-down",loading:g="lazy",fetchpriority:d="auto",class:l="",style:u="",responsive:f=!1,sizes:h=[320,640,960,1280,1920],densities:$=[1,2],preset:y,...b}=a.props;let A,_;if(y){const{ImagePresets:e}=await import("./imageOptimization_DKmSV198.mjs");A=e[y](i)}else if(f){const e=getResponsiveImageUrls(i,{sizes:h,densities:$,width:o,height:n,quality:m,format:p,fit:c});A=e.src,_=e.srcset}else{A=getOptimizedImageUrl(i,{width:o,height:n,quality:m,format:p,fit:c})}const I=f?generateSizesAttribute():void 0,z=i.startsWith("/")||i.includes("placeholder");return renderTemplate`${z?renderTemplate`<!-- Use Astro's built-in Image component for local images -->
  ${renderComponent(e,"Image",$$Image,{src:i,alt:s,width:o,height:n,loading:g,fetchpriority:d,class:l,style:u,...b})}`:renderTemplate`<!-- Use optimized external image with Cloudflare Image Transform -->
  ${maybeRenderHead()}<img${addAttribute(A,"src")}${addAttribute(_,"srcset")}${addAttribute(I,"sizes")}${addAttribute(s,"alt")}${addAttribute(o,"width")}${addAttribute(n,"height")}${addAttribute(g,"loading")}${addAttribute(d,"fetchpriority")}${addAttribute(l,"class")}${addAttribute(u,"style")}${spreadAttributes(b)}>`}`}),"D:/code/image/polar-image-store/src/components/OptimizedImage.astro",void 0);var _a,__freeze=Object.freeze,__defProp=Object.defineProperty,__template=(e,t)=>__freeze(__defProp(e,"raw",{value:__freeze(e.slice())}));const $$Astro=createAstro("https://infpik.store"),$$StructuredData=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro,t,r);a.self=$$StructuredData;const{type:i,data:s}=a.props;const o=function(e,t){const r={"@context":"https://schema.org","@type":e};switch(e){case"Product":return{...r,name:t.name,description:t.description,image:t.images||[],sku:t.id,brand:{"@type":"Brand",name:"Polar Image Store"},offers:{"@type":"Offer",price:t.price,priceCurrency:t.currency||"USD",availability:t.isAvailable?"https://schema.org/InStock":"https://schema.org/OutOfStock",seller:{"@type":"Organization",name:"Polar Image Store",url:"https://infpik.store"},url:t.url},category:"Digital Images",productID:t.id,...t.aggregateRating&&{aggregateRating:{"@type":"AggregateRating",ratingValue:t.aggregateRating.ratingValue,reviewCount:t.aggregateRating.reviewCount}}};case"Organization":return{...r,name:"Polar Image Store",url:"https://infpik.store",logo:"https://infpik.store/favicon.svg",description:"Premium digital images and artwork for creative projects",sameAs:["https://twitter.com/polarimagestore","https://facebook.com/polarimagestore"],contactPoint:{"@type":"ContactPoint",contactType:"customer service",email:"<EMAIL>"}};case"WebSite":return{...r,name:"Polar Image Store",url:"https://infpik.store",description:"Premium digital images and artwork for creative projects",publisher:{"@type":"Organization",name:"Polar Image Store"},potentialAction:{"@type":"SearchAction",target:"https://infpik.store/products?search={search_term_string}","query-input":"required name=search_term_string"}};case"BreadcrumbList":return{...r,itemListElement:t.items.map(((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:e.url})))};default:return r}}(i,s);return renderTemplate(_a||(_a=__template(['<script type="application/ld+json">',"<\/script>"])),unescapeHTML(JSON.stringify(o)))}),"D:/code/image/polar-image-store/src/components/StructuredData.astro",void 0);export{$$OptimizedImage as $,$$StructuredData as a};
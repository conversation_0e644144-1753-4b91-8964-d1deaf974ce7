globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient,t as transformPolarProduct,e as extractUniqueTags,g as getTagDisplayName}from"../../chunks/polar_CPL-REiF.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=async({url:e,locals:t})=>{try{const a=e.searchParams.get("q")||"";if(!a||a.trim().length<1)return new Response(JSON.stringify({results:[]}),{status:200,headers:{"Content-Type":"application/json","Cache-Control":"public, max-age=60"}});const r=t?.runtime?.env,s=createPolarClient(r),o=r?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca",n=await s.products.list({organizationId:o,isArchived:!1}),l=(n.result?.items||[]).map(transformPolarProduct).filter((e=>null!==e)),i=extractUniqueTags(l),c=a.toLowerCase().trim(),p=i.filter((e=>{const t=getTagDisplayName(e).toLowerCase();return e.toLowerCase().includes(c)||t.includes(c)})),u=p.map((e=>{const t=l.filter((t=>t.tags&&t.tags.includes(e))).length;return{id:e,name:getTagDisplayName(e),displayName:`#${getTagDisplayName(e)}`,count:t,url:`/products/tag/${e}`}}));u.sort(((e,t)=>{const a=e.name.toLowerCase()===c||e.id.toLowerCase()===c,r=t.name.toLowerCase()===c||t.id.toLowerCase()===c;return a&&!r?-1:!a&&r?1:t.count!==e.count?t.count-e.count:e.name.localeCompare(t.name)}));const g=u.slice(0,8);return new Response(JSON.stringify({results:g,total:p.length,query:a}),{status:200,headers:{"Content-Type":"application/json","Cache-Control":"public, max-age=60"}})}catch(e){return new Response(JSON.stringify({error:"Tag search failed",details:e instanceof Error?e.message:"Unknown error"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};
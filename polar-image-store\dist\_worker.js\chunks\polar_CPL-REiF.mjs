globalThis.process??={},globalThis.process.env??={};import{P as Polar}from"./sdk_DEQ9AU5A.mjs";function createPolarClient(e){const t=e?.POLAR_ACCESS_TOKEN||"polar_oat_igptjJXuxXOyeTQmpqxDK5QHFnnU0JNNPS1Tc3eHD7u";if(!t)throw new Error("POLAR_ACCESS_TOKEN is required");return new Polar({accessToken:t,server:"production"})}function transformPolarProduct(e){if(!e||!e.id||!e.name)return console.warn("Invalid polar product:",e),null;const t=e.prices?.[0],a=t?.priceAmount||0,r=t?.priceCurrency||"USD",n=e.metadata?.category?e.metadata.category.trim():null,o=[...e.metadata?.tags?e.metadata.tags.split(",").map((e=>e.trim())):[],...extractTags(e.description||"")].filter(Boolean);return{id:e.id,name:e.name,description:e.description||"",price:a/100,currency:r,images:e.medias?.map((e=>e.publicUrl))||[],slug:generateSlug(e.name),isAvailable:!e.isArchived,tags:o,category:n,createdAt:e.createdAt,updatedAt:e.modifiedAt||e.createdAt}}function generateSlug(e){return e&&"string"==typeof e?e.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,""):""}function extractTags(e){const t=e.match(/#(\w+)/g);return t?t.map((e=>e.slice(1))):[]}function formatPrice(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t.toUpperCase()}).format(e)}function getProductsByCategory(e,t){return"all"===t?e:e.filter((e=>e.category===t))}function getCategoryDisplayName(e){return e.split("-").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join(" ")}function generateCategoriesWithCounts(e){const t=new Map;e.forEach((e=>{if(e.category){const a=t.get(e.category)||0;t.set(e.category,a+1)}}));const a=Array.from(t.entries()).map((([e,t])=>({id:e,name:getCategoryDisplayName(e),count:t})));return a.sort(((e,t)=>e.name.localeCompare(t.name))),a.unshift({id:"all",name:"All",count:e.length}),a}function extractUniqueTags(e){return e.flatMap((e=>e.tags||[])).filter(Boolean).filter(((e,t,a)=>a.indexOf(e)===t)).sort()}function getProductsByTag(e,t){return"all"===t?e:e.filter((e=>e.tags&&e.tags.includes(t)))}function getTagDisplayName(e){return e.split("-").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join(" ")}function generateTagsWithCounts(e){const t=new Map;e.forEach((e=>{e.tags&&e.tags.forEach((e=>{const a=t.get(e)||0;t.set(e,a+1)}))}));const a=Array.from(t.entries()).map((([e,t])=>({id:e,name:getTagDisplayName(e),count:t})));return a.sort(((e,t)=>t.count!==e.count?t.count-e.count:e.name.localeCompare(t.name))),a.unshift({id:"all",name:"All Tags",count:e.length}),a}export{getProductsByCategory as a,getCategoryDisplayName as b,createPolarClient as c,getProductsByTag as d,extractUniqueTags as e,generateCategoriesWithCounts as f,getTagDisplayName as g,generateTagsWithCounts as h,formatPrice as i,transformPolarProduct as t};
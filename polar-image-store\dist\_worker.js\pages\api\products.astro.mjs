globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient,t as transformPolarProduct}from"../../chunks/polar_CPL-REiF.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=async({locals:e})=>{try{const r=e?.runtime?.env,t=createPolarClient(r),o=r?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca",a=await t.products.list({organizationId:o,isArchived:!1}),s=(a.result?.items||[]).map(transformPolarProduct);return new Response(JSON.stringify({products:s}),{status:200,headers:{"Content-Type":"application/json","Cache-Control":"public, max-age=300"}})}catch(e){return console.error("Error fetching products:",e),new Response(JSON.stringify({error:"Failed to fetch products"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};
'use strict';

const AnPlusB = require('./AnPlusB.cjs');
const Atrule = require('./Atrule.cjs');
const AtrulePrelude = require('./AtrulePrelude.cjs');
const AttributeSelector = require('./AttributeSelector.cjs');
const Block = require('./Block.cjs');
const Brackets = require('./Brackets.cjs');
const CDC = require('./CDC.cjs');
const CDO = require('./CDO.cjs');
const ClassSelector = require('./ClassSelector.cjs');
const Combinator = require('./Combinator.cjs');
const Comment = require('./Comment.cjs');
const Condition = require('./Condition.cjs');
const Declaration = require('./Declaration.cjs');
const DeclarationList = require('./DeclarationList.cjs');
const Dimension = require('./Dimension.cjs');
const Feature = require('./Feature.cjs');
const FeatureFunction = require('./FeatureFunction.cjs');
const FeatureRange = require('./FeatureRange.cjs');
const Function = require('./Function.cjs');
const GeneralEnclosed = require('./GeneralEnclosed.cjs');
const Hash = require('./Hash.cjs');
const Identifier = require('./Identifier.cjs');
const IdSelector = require('./IdSelector.cjs');
const Layer = require('./Layer.cjs');
const LayerList = require('./LayerList.cjs');
const MediaQuery = require('./MediaQuery.cjs');
const MediaQueryList = require('./MediaQueryList.cjs');
const NestingSelector = require('./NestingSelector.cjs');
const Nth = require('./Nth.cjs');
const Number = require('./Number.cjs');
const Operator = require('./Operator.cjs');
const Parentheses = require('./Parentheses.cjs');
const Percentage = require('./Percentage.cjs');
const PseudoClassSelector = require('./PseudoClassSelector.cjs');
const PseudoElementSelector = require('./PseudoElementSelector.cjs');
const Ratio = require('./Ratio.cjs');
const Raw = require('./Raw.cjs');
const Rule = require('./Rule.cjs');
const Scope = require('./Scope.cjs');
const Selector = require('./Selector.cjs');
const SelectorList = require('./SelectorList.cjs');
const String = require('./String.cjs');
const StyleSheet = require('./StyleSheet.cjs');
const SupportsDeclaration = require('./SupportsDeclaration.cjs');
const TypeSelector = require('./TypeSelector.cjs');
const UnicodeRange = require('./UnicodeRange.cjs');
const Url = require('./Url.cjs');
const Value = require('./Value.cjs');
const WhiteSpace = require('./WhiteSpace.cjs');



exports.AnPlusB = AnPlusB.generate;
exports.Atrule = Atrule.generate;
exports.AtrulePrelude = AtrulePrelude.generate;
exports.AttributeSelector = AttributeSelector.generate;
exports.Block = Block.generate;
exports.Brackets = Brackets.generate;
exports.CDC = CDC.generate;
exports.CDO = CDO.generate;
exports.ClassSelector = ClassSelector.generate;
exports.Combinator = Combinator.generate;
exports.Comment = Comment.generate;
exports.Condition = Condition.generate;
exports.Declaration = Declaration.generate;
exports.DeclarationList = DeclarationList.generate;
exports.Dimension = Dimension.generate;
exports.Feature = Feature.generate;
exports.FeatureFunction = FeatureFunction.generate;
exports.FeatureRange = FeatureRange.generate;
exports.Function = Function.generate;
exports.GeneralEnclosed = GeneralEnclosed.generate;
exports.Hash = Hash.generate;
exports.Identifier = Identifier.generate;
exports.IdSelector = IdSelector.generate;
exports.Layer = Layer.generate;
exports.LayerList = LayerList.generate;
exports.MediaQuery = MediaQuery.generate;
exports.MediaQueryList = MediaQueryList.generate;
exports.NestingSelector = NestingSelector.generate;
exports.Nth = Nth.generate;
exports.Number = Number.generate;
exports.Operator = Operator.generate;
exports.Parentheses = Parentheses.generate;
exports.Percentage = Percentage.generate;
exports.PseudoClassSelector = PseudoClassSelector.generate;
exports.PseudoElementSelector = PseudoElementSelector.generate;
exports.Ratio = Ratio.generate;
exports.Raw = Raw.generate;
exports.Rule = Rule.generate;
exports.Scope = Scope.generate;
exports.Selector = Selector.generate;
exports.SelectorList = SelectorList.generate;
exports.String = String.generate;
exports.StyleSheet = StyleSheet.generate;
exports.SupportsDeclaration = SupportsDeclaration.generate;
exports.TypeSelector = TypeSelector.generate;
exports.UnicodeRange = UnicodeRange.generate;
exports.Url = Url.generate;
exports.Value = Value.generate;
exports.WhiteSpace = WhiteSpace.generate;
